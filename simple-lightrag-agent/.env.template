# LightRAG Agent Environment Configuration Template
# Copy this file to .env and customize for your environment

# AWS Configuration
AWS_PROFILE=IA
AWS_REGION=us-east-2

# Model Configuration
LLM_MODEL=us.amazon.nova-micro-v1:0
EMBEDDING_MODEL=amazon.titan-embed-text-v2:0

# Google AI Studio Configuration
GOOGLE_AI_STUDIO_API_KEY=AIzaSyDUuidjJIN5hYDvPycVUV0_4JBEziSIgi4
GOOGLE_AI_MODEL=gemini-1.5-flash

# ADK Configuration
ADK_LLM_PROVIDER=google_ai_studio
ADK_ENABLE_FALLBACK=true

# LLM Parameters
LLM_TEMPERATURE=0.1
LLM_MAX_TOKENS=4000
LLM_TOP_P=0.9

# Embedding Configuration
EMBEDDING_DIMENSIONS=1024
EMBEDDING_NORMALIZE=true

# Document Configuration
DOCS_PATH=/home/<USER>/dropi-ubuntu/agent-v2/docs
SUPPORTED_EXTENSIONS=.docx,.pdf,.txt
LIGHTRAG_WORKING_DIR=./lightrag_data

# Validation Thresholds (adjust based on your document corpus)
MIN_ENTITIES=1
MIN_RELATIONSHIPS=1
MIN_CHUNKS=1
MIN_RESPONSE_LENGTH=50
GOOD_RESPONSE_LENGTH=200

# Custom Keywords (comma-separated)
# Vector Keywords (for document retrieval validation)
CUSTOM_VECTOR_KEYWORDS=empresa,compañía,organización,startup

# Entity Keywords (for knowledge graph validation)
CUSTOM_ENTITY_KEYWORDS=funcionalidad,capacidad,módulo,componente

# Domain Keywords (for domain-specific validation)
CUSTOM_DOMAIN_KEYWORDS=negocio,comercio,mercado,cliente

# Performance Settings
QUERY_TIMEOUT=30
DEMO_DELAY=2
MAX_RETRIES=3

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
