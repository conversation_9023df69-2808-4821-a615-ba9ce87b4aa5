"""
Google ADK Agent integration with LightRAG Bedrock and Google AI Studio
"""
import asyncio
import logging
import os
from typing import Dict, Any, Optional

# Google ADK imports with enhanced configuration support
try:
    from google.adk.core import Agent
    from google.adk.core.tools import BaseTool
    from google.adk.core.llm import LLMConfig
    ADK_AVAILABLE = True
except ImportError:
    # Fallback if ADK is not available
    ADK_AVAILABLE = False
    BaseTool = object
    Agent = object
    LLMConfig = object

# Separate import for Google Generative AI
try:
    import google.generativeai as genai
    GENAI_AVAILABLE = True
except ImportError:
    GENAI_AVAILABLE = False
    genai = None

from lightrag_bedrock_fixed import FixedLightRAGBedrock
from config import (
    DOCS_PATH, LLM_MODEL, GOOGLE_AI_STUDIO_API_KEY, GOOGLE_AI_MODEL,
    ADK_LLM_PROVIDER, ADK_ENABLE_FALLBACK
)

logger = logging.getLogger(__name__)

class LightRAGQueryTool(BaseTool):
    """ADK Tool for querying LightRAG"""
    
    def __init__(self, lightrag_instance: FixedLightRAGBedrock):
        if ADK_AVAILABLE:
            super().__init__(
                name="lightrag_query",
                description="Query documents using LightRAG with Bedrock",
                parameters={
                    "question": {"type": "string", "description": "Question to ask about the documents"},
                    "mode": {"type": "string", "description": "Query mode: naive, local, global, or hybrid", "default": "hybrid"}
                }
            )
        self.lightrag = lightrag_instance
        self.logger = logging.getLogger(__name__)
    
    async def execute(self, question: str, mode: str = "hybrid") -> Dict[str, Any]:
        """Execute LightRAG query"""
        try:
            self.logger.info(f"ADK Tool executing query: {question}")
            
            response = await self.lightrag.query(question, mode)
            
            return {
                "success": True,
                "response": response,
                "question": question,
                "mode": mode
            }
            
        except Exception as e:
            self.logger.error(f"ADK Tool query failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "question": question
            }

class DocumentLoaderTool(BaseTool):
    """ADK Tool for loading documents"""
    
    def __init__(self, lightrag_instance: FixedLightRAGBedrock):
        if ADK_AVAILABLE:
            super().__init__(
                name="load_documents",
                description="Load .docx documents into LightRAG",
                parameters={
                    "docs_path": {"type": "string", "description": "Path to documents directory", "default": DOCS_PATH}
                }
            )
        self.lightrag = lightrag_instance
        self.logger = logging.getLogger(__name__)
    
    async def execute(self, docs_path: str = DOCS_PATH) -> Dict[str, Any]:
        """Execute document loading"""
        try:
            self.logger.info(f"ADK Tool loading documents from: {docs_path}")
            
            success = await self.lightrag.ingest_documents(docs_path)
            
            return {
                "success": success,
                "message": "Documents loaded successfully" if success else "Failed to load documents",
                "docs_path": docs_path
            }
            
        except Exception as e:
            self.logger.error(f"ADK Tool document loading failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "docs_path": docs_path
            }

class SimpleLightRAGAgent:
    """Simplified ADK Agent for LightRAG with Bedrock and Google AI Studio"""

    def __init__(self, llm_provider: str = None):
        self.lightrag = FixedLightRAGBedrock()
        self.agent = None
        self.logger = logging.getLogger(__name__)
        self.initialized = False
        self.llm_provider = llm_provider or ADK_LLM_PROVIDER
        self.google_ai_configured = False
    
    async def initialize(self):
        """Initialize the agent and LightRAG"""
        try:
            self.logger.info(f"Initializing SimpleLightRAGAgent with {self.llm_provider} provider...")

            # Configure Google AI Studio if needed
            if self.llm_provider == "google_ai_studio":
                self._configure_google_ai_studio()

            # Initialize LightRAG
            success = await self.lightrag.initialize()
            if not success:
                raise Exception("Failed to initialize LightRAG")

            # Create tools
            self.query_tool = LightRAGQueryTool(self.lightrag)
            self.loader_tool = DocumentLoaderTool(self.lightrag)

            # Initialize ADK Agent if available
            if ADK_AVAILABLE:
                self._setup_adk_agent()
            else:
                self.logger.warning("Google ADK not available - using direct LightRAG mode")

            self.initialized = True
            self.logger.info("SimpleLightRAGAgent initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize SimpleLightRAGAgent: {e}")
            return False
    
    def _configure_google_ai_studio(self):
        """Configure Google AI Studio authentication"""
        try:
            if GENAI_AVAILABLE and GOOGLE_AI_STUDIO_API_KEY:
                genai.configure(api_key=GOOGLE_AI_STUDIO_API_KEY)
                self.google_ai_configured = True
                self.logger.info("✅ Google AI Studio configured successfully")
            else:
                self.logger.warning("⚠️ Google AI Studio not available or API key missing")
                if ADK_ENABLE_FALLBACK:
                    self.logger.info("🔄 Falling back to AWS Bedrock for ADK agent")
                    self.llm_provider = "bedrock"
        except Exception as e:
            self.logger.error(f"Failed to configure Google AI Studio: {e}")
            if ADK_ENABLE_FALLBACK:
                self.logger.info("🔄 Falling back to AWS Bedrock for ADK agent")
                self.llm_provider = "bedrock"

    def _setup_adk_agent(self):
        """Setup Google ADK Agent with configurable LLM provider"""
        try:
            # Configure LLM based on provider
            if self.llm_provider == "google_ai_studio" and self.google_ai_configured:
                llm_config = LLMConfig(
                    model=GOOGLE_AI_MODEL,
                    temperature=0.1,
                    max_tokens=4000,
                    api_key=GOOGLE_AI_STUDIO_API_KEY
                )
                agent_name = "LightRAG Google AI Studio Agent"
                agent_description = "Agent that uses LightRAG with Google AI Studio for document Q&A with Spanish language support"
                self.logger.info(f"🤖 Configuring ADK with Google AI Studio ({GOOGLE_AI_MODEL})")
            else:
                # Fallback to Bedrock configuration
                llm_config = LLMConfig(
                    model=LLM_MODEL,
                    temperature=0.1,
                    max_tokens=4000
                )
                agent_name = "LightRAG Bedrock Agent"
                agent_description = "Agent that uses LightRAG with AWS Bedrock for document Q&A with Spanish language support"
                self.logger.info(f"🤖 Configuring ADK with AWS Bedrock ({LLM_MODEL})")

            # Create tools list
            tools = [self.query_tool, self.loader_tool]

            # Create ADK agent with dynamic configuration
            self.agent = Agent(
                name=agent_name,
                description=agent_description,
                llm_config=llm_config,
                tools=tools
            )

            self.logger.info(f"✅ ADK Agent configured successfully with {self.llm_provider}")

        except Exception as e:
            self.logger.error(f"Failed to setup ADK Agent: {e}")
            if ADK_ENABLE_FALLBACK and self.llm_provider == "google_ai_studio":
                self.logger.info("🔄 Attempting fallback to Bedrock configuration")
                self.llm_provider = "bedrock"
                self._setup_adk_agent()  # Retry with Bedrock
    
    async def load_documents(self, docs_path: str = DOCS_PATH) -> bool:
        """Load documents into the system"""
        if not self.initialized:
            await self.initialize()

        result = await self.loader_tool.execute(docs_path)
        return result.get("success", False)

    async def query(self, question: str, mode: str = "hybrid") -> str:
        """Query the document system with enhanced ADK integration"""
        if not self.initialized:
            await self.initialize()

        # Log the query with provider information
        self.logger.info(f"🤔 ADK Query ({self.llm_provider}): {question}")

        # Execute query through LightRAG (maintains hybrid RAG functionality)
        result = await self.query_tool.execute(question, mode)

        if result.get("success"):
            response = result.get("response", "No response generated")

            # Log response quality for Spanish queries
            if any(spanish_word in question.lower() for spanish_word in ["qué", "cuál", "cómo", "dropi"]):
                self.logger.info(f"🇪🇸 Spanish query processed - Response length: {len(response)} chars")

            return response
        else:
            error_msg = f"Error: {result.get('error', 'Unknown error')}"
            self.logger.error(f"❌ ADK Query failed: {error_msg}")
            return error_msg

    async def test_google_ai_studio_connection(self) -> Dict[str, Any]:
        """Test Google AI Studio API connection"""
        if not GENAI_AVAILABLE:
            return {
                "success": False,
                "error": "Google Generative AI library not available",
                "provider": "none"
            }

        try:
            if self.llm_provider == "google_ai_studio" and self.google_ai_configured:
                # Test a simple API call
                model = genai.GenerativeModel(GOOGLE_AI_MODEL)
                test_response = model.generate_content("Hello, respond with 'API connection successful'")

                return {
                    "success": True,
                    "response": test_response.text,
                    "model": GOOGLE_AI_MODEL,
                    "provider": "google_ai_studio"
                }
            else:
                return {
                    "success": False,
                    "error": "Google AI Studio not configured or not selected as provider",
                    "provider": self.llm_provider
                }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "provider": self.llm_provider
            }

    def get_configuration_info(self) -> Dict[str, Any]:
        """Get current configuration information"""
        return {
            "llm_provider": self.llm_provider,
            "adk_available": ADK_AVAILABLE,
            "genai_available": GENAI_AVAILABLE,
            "google_ai_configured": self.google_ai_configured,
            "google_ai_model": GOOGLE_AI_MODEL if self.llm_provider == "google_ai_studio" else None,
            "bedrock_model": LLM_MODEL if self.llm_provider == "bedrock" else None,
            "fallback_enabled": ADK_ENABLE_FALLBACK,
            "initialized": self.initialized
        }
