"""
Configuration for simplified LightRAG agent with AWS Bedrock
"""
import os
import boto3
from pathlib import Path
from typing import List, Dict, Any, Optional

# AWS Bedrock Configuration (using environment variables with fallbacks)
AWS_PROFILE = os.getenv("AWS_PROFILE", "IA")
AWS_REGION = os.getenv("AWS_REGION", "us-east-2")

# Validated Bedrock Models (configurable via environment)
LLM_MODEL = os.getenv("LLM_MODEL", "us.amazon.nova-micro-v1:0")
EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "amazon.titan-embed-text-v2:0")

# Google AI Studio Configuration
GOOGLE_AI_STUDIO_API_KEY = os.getenv("GOOGLE_AI_STUDIO_API_KEY", "AIzaSyDUuidjJIN5hYDvPycVUV0_4JBEziSIgi4")
GOOGLE_AI_MODEL = os.getenv("GOOGLE_AI_MODEL", "gemini-1.5-flash")

# ADK Configuration
ADK_LLM_PROVIDER = os.getenv("ADK_LLM_PROVIDER", "google_ai_studio")  # Options: "bedrock", "google_ai_studio"
ADK_ENABLE_FALLBACK = bool(os.getenv("ADK_ENABLE_FALLBACK", "True").lower() == "true")

# LLM Configuration (configurable parameters)
LLM_CONFIG = {
    "provider": "bedrock",
    "model": LLM_MODEL,
    "format": "Nova",
    "region": AWS_REGION,
    "profile": AWS_PROFILE,
    "temperature": float(os.getenv("LLM_TEMPERATURE", "0.1")),
    "max_tokens": int(os.getenv("LLM_MAX_TOKENS", "4000")),
    "top_p": float(os.getenv("LLM_TOP_P", "0.9")),
}

# Embedding Configuration (configurable dimensions)
EMBEDDING_CONFIG = {
    "provider": "bedrock",
    "model": EMBEDDING_MODEL,
    "format": "Titan Embed",
    "region": AWS_REGION,
    "profile": AWS_PROFILE,
    "dimensions": int(os.getenv("EMBEDDING_DIMENSIONS", "1024")),
    "normalize": bool(os.getenv("EMBEDDING_NORMALIZE", "True").lower() == "true"),
}

# Document Configuration (dynamic discovery)
DOCS_PATH = os.getenv("DOCS_PATH", "/home/<USER>/dropi-ubuntu/agent-v2/docs")
SUPPORTED_EXTENSIONS = os.getenv("SUPPORTED_EXTENSIONS", ".docx,.pdf,.txt").split(",")

# Working Directory for LightRAG (configurable)
WORKING_DIR = Path(os.getenv("LIGHTRAG_WORKING_DIR", "./lightrag_data"))

# Validation Configuration (dynamic thresholds)
VALIDATION_CONFIG = {
    "min_entities": int(os.getenv("MIN_ENTITIES", "1")),
    "min_relationships": int(os.getenv("MIN_RELATIONSHIPS", "1")),
    "min_chunks": int(os.getenv("MIN_CHUNKS", "1")),
    "min_response_length": int(os.getenv("MIN_RESPONSE_LENGTH", "50")),
    "good_response_length": int(os.getenv("GOOD_RESPONSE_LENGTH", "200")),
}

# Multilingual Keyword Configuration
KEYWORD_CONFIG = {
    "vector_keywords": {
        "english": ["platform", "service", "tool", "system", "solution", "technology"],
        "spanish": ["plataforma", "servicio", "herramienta", "sistema", "solución", "tecnología"],
        "custom": os.getenv("CUSTOM_VECTOR_KEYWORDS", "").split(",") if os.getenv("CUSTOM_VECTOR_KEYWORDS") else []
    },
    "entity_keywords": {
        "english": ["feature", "education", "learning", "training", "course", "tutorial"],
        "spanish": ["característica", "educación", "aprendizaje", "entrenamiento", "curso", "tutorial"],
        "custom": os.getenv("CUSTOM_ENTITY_KEYWORDS", "").split(",") if os.getenv("CUSTOM_ENTITY_KEYWORDS") else []
    },
    "domain_keywords": {
        "english": ["business", "commerce", "logistics", "delivery", "shipping"],
        "spanish": ["negocio", "comercio", "logística", "entrega", "envío"],
        "custom": os.getenv("CUSTOM_DOMAIN_KEYWORDS", "").split(",") if os.getenv("CUSTOM_DOMAIN_KEYWORDS") else []
    }
}

def get_all_keywords() -> List[str]:
    """Get all keywords from all languages and categories for validation"""
    all_keywords = []
    for category in KEYWORD_CONFIG.values():
        for lang_keywords in category.values():
            if isinstance(lang_keywords, list):
                all_keywords.extend([kw.strip() for kw in lang_keywords if kw.strip()])
    return list(set(all_keywords))  # Remove duplicates

def get_keywords_by_category(category: str) -> List[str]:
    """Get keywords for a specific category (vector, entity, domain)"""
    if category not in KEYWORD_CONFIG:
        return []

    keywords = []
    for lang_keywords in KEYWORD_CONFIG[category].values():
        if isinstance(lang_keywords, list):
            keywords.extend([kw.strip() for kw in lang_keywords if kw.strip()])
    return list(set(keywords))

def discover_documents(docs_path: str = None) -> Dict[str, Any]:
    """Dynamically discover documents in the specified path"""
    if docs_path is None:
        docs_path = DOCS_PATH

    docs_path = Path(docs_path)
    if not docs_path.exists():
        return {"count": 0, "files": [], "extensions": [], "total_size": 0}

    discovered_files = []
    extensions_found = set()
    total_size = 0

    for ext in SUPPORTED_EXTENSIONS:
        ext = ext.strip()
        if not ext.startswith('.'):
            ext = '.' + ext

        for file_path in docs_path.rglob(f"*{ext}"):
            if file_path.is_file():
                file_info = {
                    "path": str(file_path),
                    "name": file_path.name,
                    "size": file_path.stat().st_size,
                    "extension": file_path.suffix
                }
                discovered_files.append(file_info)
                extensions_found.add(file_path.suffix)
                total_size += file_info["size"]

    return {
        "count": len(discovered_files),
        "files": discovered_files,
        "extensions": list(extensions_found),
        "total_size": total_size,
        "path": str(docs_path)
    }

def get_bedrock_client():
    """Initialize and return Bedrock client"""
    try:
        session = boto3.Session(profile_name=AWS_PROFILE)
        client = session.client('bedrock-runtime', region_name=AWS_REGION)
        return client
    except Exception as e:
        raise Exception(f"Failed to initialize Bedrock client: {e}")

def validate_bedrock_access():
    """Validate access to Bedrock models"""
    try:
        client = get_bedrock_client()
        # Simple validation - just check if client is created successfully
        return True
    except Exception as e:
        print(f"Bedrock access validation failed: {e}")
        return False
